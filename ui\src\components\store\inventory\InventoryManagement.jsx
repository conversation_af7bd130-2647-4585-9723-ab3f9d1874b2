import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  LinearProgress,
  Avatar,
  Divider,
  Tab,
  Tabs,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Inventory,
  Warning,
  TrendingUp,
  TrendingDown,
  Add,
  Edit,
  Delete,
  Visibility,
  Refresh,
  Search,
  FilterList,
  Download,
  Upload,
  Notifications,
  LocalShipping,
  Store,
  Assessment,
  Timeline,
  BarChart,
  PieChart,
  Business,
  Schedule,
  CheckCircle,
  Error,
  Info
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';

const InventoryManagement = ({ storeId }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [inventory, setInventory] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [stockAlerts, setStockAlerts] = useState([]);
  const [inventoryAnalytics, setInventoryAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);

  useEffect(() => {
    fetchInventoryData();
  }, [storeId]);

  const fetchInventoryData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      // Fetch inventory data
      const inventoryResponse = await fetch(`http://localhost:5000/api/inventory/store/${storeId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (inventoryResponse.ok) {
        const inventoryData = await inventoryResponse.json();
        setInventory(inventoryData.inventory || []);
      }

      // Fetch suppliers
      const suppliersResponse = await fetch(`http://localhost:5000/api/inventory/store/${storeId}/suppliers`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (suppliersResponse.ok) {
        const suppliersData = await suppliersResponse.json();
        setSuppliers(suppliersData.suppliers || []);
      }

      // Fetch stock alerts
      const alertsResponse = await fetch(`http://localhost:5000/api/inventory/store/${storeId}/alerts`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setStockAlerts(alertsData.alerts || []);
      }

      // Fetch inventory analytics
      const analyticsResponse = await fetch(`http://localhost:5000/api/inventory/store/${storeId}/analytics`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (analyticsResponse.ok) {
        const analyticsData = await analyticsResponse.json();
        setInventoryAnalytics(analyticsData.analytics || {});
      }
    } catch (error) {
      console.error('Error fetching inventory data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStockStatusColor = (status) => {
    switch (status) {
      case 'in_stock': return 'success';
      case 'low_stock': return 'warning';
      case 'critical': return 'error';
      case 'out_of_stock': return 'error';
      default: return 'default';
    }
  };

  const getStockStatusText = (status) => {
    switch (status) {
      case 'in_stock': return 'In Stock';
      case 'low_stock': return 'Low Stock';
      case 'critical': return 'Critical';
      case 'out_of_stock': return 'Out of Stock';
      default: return 'Unknown';
    }
  };

  const MetricCard = ({ title, value, change, icon, color = 'primary', format = 'number' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {format === 'currency' ? `R${value?.toLocaleString() || 0}` : 
               format === 'percentage' ? `${value || 0}%` : 
               value?.toLocaleString() || 0}
            </Typography>
            {change !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {change >= 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={change >= 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.light` }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Inventory Management</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchInventoryData}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<Download />}
          >
            Export
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
          >
            Add Product
          </Button>
        </Box>
      </Box>

      {/* Inventory Analytics Overview */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Products"
            value={inventoryAnalytics?.totalProducts}
            icon={<Inventory />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Inventory Value"
            value={inventoryAnalytics?.totalValue}
            format="currency"
            icon={<Assessment />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Low Stock Items"
            value={inventoryAnalytics?.lowStockItems}
            icon={<Warning />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Turnover Rate"
            value={inventoryAnalytics?.turnoverRate}
            format="number"
            icon={<Timeline />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Stock Alerts */}
      {stockAlerts.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Stock Alerts
            </Typography>
            {stockAlerts.map((alert) => (
              <Alert 
                key={alert.id} 
                severity={alert.severity} 
                sx={{ mb: 1 }}
                action={
                  <Button size="small">
                    Reorder
                  </Button>
                }
              >
                <strong>{alert.productName}:</strong> {alert.message}
                <Typography variant="caption" display="block">
                  {alert.timestamp}
                </Typography>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            label="Stock Overview"
            icon={<Inventory />}
            iconPosition="start"
          />
          <Tab
            label="Suppliers"
            icon={<Business />}
            iconPosition="start"
          />
          <Tab
            label="Reorder Management"
            icon={<LocalShipping />}
            iconPosition="start"
          />
          <Tab
            label="Analytics"
            icon={<BarChart />}
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* Stock Overview Tab */}
          {activeTab === 0 && (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">Stock Overview</Typography>
                <Box display="flex" gap={2}>
                  <TextField
                    size="small"
                    placeholder="Search products..."
                    InputProps={{
                      startAdornment: <Search />
                    }}
                  />
                  <Button startIcon={<FilterList />}>Filter</Button>
                </Box>
              </Box>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell align="right">Current Stock</TableCell>
                      <TableCell align="right">Reorder Point</TableCell>
                      <TableCell align="right">Unit Cost</TableCell>
                      <TableCell align="right">Selling Price</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {inventory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {item.name}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {item.barcode}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{item.category}</TableCell>
                        <TableCell align="right">
                          <Box display="flex" alignItems="center" justifyContent="flex-end">
                            {item.currentStock}
                            {item.currentStock <= item.reorderPoint && (
                              <Warning color="warning" fontSize="small" sx={{ ml: 1 }} />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell align="right">{item.reorderPoint}</TableCell>
                        <TableCell align="right">R{item.unitCost.toFixed(2)}</TableCell>
                        <TableCell align="right">R{item.sellingPrice.toFixed(2)}</TableCell>
                        <TableCell>
                          <Chip
                            label={getStockStatusText(item.status)}
                            color={getStockStatusColor(item.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Box display="flex" gap={1}>
                            <IconButton size="small">
                              <Edit />
                            </IconButton>
                            <IconButton size="small">
                              <Visibility />
                            </IconButton>
                            <IconButton size="small" color="primary">
                              <LocalShipping />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Suppliers Tab */}
          {activeTab === 1 && (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">Supplier Management</Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                >
                  Add Supplier
                </Button>
              </Box>

              <Grid container spacing={3}>
                {suppliers.map((supplier) => (
                  <Grid item xs={12} md={6} key={supplier.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Typography variant="h6">{supplier.name}</Typography>
                          <Chip
                            label={supplier.status}
                            color={supplier.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>

                        <Typography variant="body2" color="textSecondary" mb={1}>
                          Contact: {supplier.contact}
                        </Typography>
                        <Typography variant="body2" color="textSecondary" mb={1}>
                          Email: {supplier.email}
                        </Typography>
                        <Typography variant="body2" color="textSecondary" mb={2}>
                          Phone: {supplier.phone}
                        </Typography>

                        <Grid container spacing={2} mb={2}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Products</Typography>
                            <Typography variant="h6">{supplier.products}</Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Last Order</Typography>
                            <Typography variant="h6">{supplier.lastOrder}</Typography>
                          </Grid>
                        </Grid>

                        <Box display="flex" gap={1}>
                          <Button size="small" startIcon={<Edit />}>Edit</Button>
                          <Button size="small" startIcon={<Visibility />}>View Products</Button>
                          <Button size="small" startIcon={<LocalShipping />}>New Order</Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Reorder Management Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" mb={3}>Reorder Management</Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Automatic Reorder Settings</Typography>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Enable automatic reordering"
                      />
                      <Typography variant="body2" color="textSecondary" mt={2}>
                        Automatically create purchase orders when stock reaches reorder point
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Pending Orders</Typography>
                      <Typography variant="h4" color="primary">
                        5
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Orders awaiting approval
                      </Typography>
                      <Button variant="outlined" size="small" sx={{ mt: 2 }}>
                        View Orders
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Analytics Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" mb={3}>Inventory Analytics</Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Category Distribution</Typography>
                      {inventoryAnalytics?.topCategories.map((category) => (
                        <Box key={category.name} mb={2}>
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography variant="body2">{category.name}</Typography>
                            <Typography variant="body2">{category.percentage}%</Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={category.percentage}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                        </Box>
                      ))}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Performance Metrics</Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="textSecondary">Avg Restock Time</Typography>
                          <Typography variant="h5">{inventoryAnalytics?.avgDaysToRestock} days</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="textSecondary">Out of Stock</Typography>
                          <Typography variant="h5">{inventoryAnalytics?.outOfStockItems}</Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </Box>
      </Card>
    </Box>
  );
};

export default InventoryManagement;
