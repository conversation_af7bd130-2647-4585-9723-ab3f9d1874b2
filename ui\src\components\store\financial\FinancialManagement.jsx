import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AttachMoney,
  Receipt,
  AccountBalance,
  CreditCard,
  Download,
  Refresh,
  Warning,
  CheckCircle
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

const FinancialManagement = ({ storeId }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [financialData, setFinancialData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('month');

  useEffect(() => {
    fetchFinancialData();
  }, [storeId, timeframe]);

  const fetchFinancialData = async () => {
    try {
      const response = await fetch(
        `http://localhost:5000/api/financial/store/${storeId}?timeframe=${timeframe}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      const data = await response.json();
      setFinancialData(data);
    } catch (error) {
      console.error('Error fetching financial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const MetricCard = ({ title, value, change, icon, color = 'primary', format = 'currency' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {format === 'currency' ? `R${value?.toLocaleString() || 0}` : value || 0}
            </Typography>
            {change !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {change >= 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={change >= 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Box
            sx={{
              backgroundColor: `${color}.light`,
              borderRadius: 2,
              p: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  const RevenueChart = ({ data }) => (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis />
        <RechartsTooltip formatter={(value) => [`R${value}`, 'Revenue']} />
        <Area
          type="monotone"
          dataKey="revenue"
          stroke="#8884d8"
          fill="#8884d8"
          fillOpacity={0.6}
        />
        <Area
          type="monotone"
          dataKey="profit"
          stroke="#82ca9d"
          fill="#82ca9d"
          fillOpacity={0.6}
        />
      </AreaChart>
    </ResponsiveContainer>
  );

  const ExpenseBreakdown = ({ data }) => {
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];
    
    return (
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data?.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <RechartsTooltip formatter={(value) => [`R${value}`, 'Amount']} />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  const PayFastFeesTable = ({ fees }) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Transaction Type</TableCell>
            <TableCell align="right">Count</TableCell>
            <TableCell align="right">Total Amount</TableCell>
            <TableCell align="right">Fees</TableCell>
            <TableCell align="right">Net Amount</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {fees?.map((fee, index) => (
            <TableRow key={index}>
              <TableCell>{fee.type}</TableCell>
              <TableCell align="right">{fee.count}</TableCell>
              <TableCell align="right">R{fee.totalAmount.toLocaleString()}</TableCell>
              <TableCell align="right" sx={{ color: 'error.main' }}>
                R{fee.fees.toLocaleString()}
              </TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                R{fee.netAmount.toLocaleString()}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const TaxSummary = ({ taxData }) => (
    <Grid container spacing={2}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              VAT Summary
            </Typography>
            <Box mb={2}>
              <Typography variant="body2" color="textSecondary">
                VAT Collected
              </Typography>
              <Typography variant="h5" color="primary">
                R{taxData?.vatCollected?.toLocaleString() || 0}
              </Typography>
            </Box>
            <Box mb={2}>
              <Typography variant="body2" color="textSecondary">
                VAT Rate
              </Typography>
              <Typography variant="body1">
                {taxData?.vatRate || 15}%
              </Typography>
            </Box>
            <Button
              variant="outlined"
              startIcon={<Download />}
              onClick={() => {/* Handle VAT report download */}}
            >
              Download VAT Report
            </Button>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Tax Compliance
            </Typography>
            <Box display="flex" alignItems="center" mb={2}>
              <CheckCircle color="success" sx={{ mr: 1 }} />
              <Typography variant="body2">
                All transactions recorded
              </Typography>
            </Box>
            <Box display="flex" alignItems="center" mb={2}>
              <CheckCircle color="success" sx={{ mr: 1 }} />
              <Typography variant="body2">
                VAT calculations up to date
              </Typography>
            </Box>
            <Box display="flex" alignItems="center" mb={2}>
              <Warning color="warning" sx={{ mr: 1 }} />
              <Typography variant="body2">
                Monthly return due in 5 days
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const ProfitAnalysis = ({ profitData }) => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Profit Analysis
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Box textAlign="center" p={2}>
              <Typography variant="h4" color="success.main">
                {profitData?.grossMargin || 0}%
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Gross Margin
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box textAlign="center" p={2}>
              <Typography variant="h4" color="info.main">
                {profitData?.netMargin || 0}%
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Net Margin
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box textAlign="center" p={2}>
              <Typography variant="h4" color="primary.main">
                R{profitData?.avgTransactionProfit?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Avg Transaction Profit
              </Typography>
            </Box>
          </Grid>
        </Grid>
        <Box mt={2}>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Profit Trend
          </Typography>
          <LinearProgress
            variant="determinate"
            value={Math.min(profitData?.profitGrowth || 0, 100)}
            color={profitData?.profitGrowth >= 0 ? 'success' : 'error'}
            sx={{ height: 8, borderRadius: 4 }}
          />
          <Typography variant="caption" color="textSecondary">
            {profitData?.profitGrowth >= 0 ? '+' : ''}{profitData?.profitGrowth || 0}% vs last period
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return <LinearProgress />;
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Financial Management</Typography>
        <Box display="flex" alignItems="center" gap={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Period</InputLabel>
            <Select
              value={timeframe}
              label="Period"
              onChange={(e) => setTimeframe(e.target.value)}
            >
              <MenuItem value="week">This Week</MenuItem>
              <MenuItem value="month">This Month</MenuItem>
              <MenuItem value="quarter">This Quarter</MenuItem>
              <MenuItem value="year">This Year</MenuItem>
            </Select>
          </FormControl>
          <Tooltip title="Refresh Data">
            <IconButton onClick={fetchFinancialData}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Key Financial Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Revenue"
            value={financialData?.totalRevenue}
            change={financialData?.revenueChange}
            icon={<AttachMoney />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Net Profit"
            value={financialData?.netProfit}
            change={financialData?.profitChange}
            icon={<AccountBalance />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="PayFast Fees"
            value={financialData?.payfastFees}
            change={financialData?.feesChange}
            icon={<CreditCard />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Tax Collected"
            value={financialData?.taxCollected}
            change={financialData?.taxChange}
            icon={<Receipt />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Revenue & Profit" />
          <Tab label="PayFast Fees" />
          <Tab label="Tax Reports" />
          <Tab label="Profit Analysis" />
        </Tabs>

        <CardContent>
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Revenue & Profit Trends
              </Typography>
              <RevenueChart data={financialData?.revenueData || []} />
            </Box>
          )}

          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                PayFast Transaction Fees
              </Typography>
              <PayFastFeesTable fees={financialData?.payfastFees || []} />
              <Box mt={3}>
                <Alert severity="info">
                  PayFast charges 2.5% for local card transactions. Fees are automatically deducted from settlements.
                </Alert>
              </Box>
            </Box>
          )}

          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Tax Reports & Compliance
              </Typography>
              <TaxSummary taxData={financialData?.taxData || {}} />
            </Box>
          )}

          {activeTab === 3 && (
            <Box>
              <ProfitAnalysis profitData={financialData?.profitData || {}} />
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default FinancialManagement;
