import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  People,
  ShoppingCart,
  Schedule,
  Star,
  Refresh,
  Analytics,
  AccessTime,
  AttachMoney
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import axios from 'axios';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const MetricCard = ({ title, value, change, icon, color = 'primary', format = 'number' }) => {
  const formatValue = (val) => {
    if (format === 'currency') return `R${val?.toLocaleString() || 0}`;
    if (format === 'percentage') return `${val || 0}%`;
    if (format === 'time') return `${val || 0} min`;
    return val?.toLocaleString() || 0;
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h6" component="div">
              {formatValue(value)}
            </Typography>
            {change !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {change >= 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={change >= 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

const ShoppingPatternsChart = ({ data }) => {
  const hourlyData = data.reduce((acc, item) => {
    const hour = item.hour;
    if (!acc[hour]) {
      acc[hour] = { hour: `${hour}:00`, transactions: 0, revenue: 0 };
    }
    acc[hour].transactions += item.transactions;
    acc[hour].revenue += item.totalRevenue;
    return acc;
  }, {});

  const chartData = Object.values(hourlyData).sort((a, b) => 
    parseInt(a.hour) - parseInt(b.hour)
  );

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="hour" />
        <YAxis yAxisId="left" />
        <YAxis yAxisId="right" orientation="right" />
        <RechartsTooltip />
        <Bar yAxisId="left" dataKey="transactions" fill="#8884d8" name="Transactions" />
        <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#82ca9d" name="Revenue" />
      </BarChart>
    </ResponsiveContainer>
  );
};

const BasketSizeChart = ({ data }) => {
  if (!data || !data.totalTransactions) return <Typography>No data available</Typography>;

  const chartData = [
    { name: 'Small (1-3 items)', value: data.smallBaskets, percentage: ((data.smallBaskets / data.totalTransactions) * 100).toFixed(1) },
    { name: 'Medium (4-10 items)', value: data.mediumBaskets, percentage: ((data.mediumBaskets / data.totalTransactions) * 100).toFixed(1) },
    { name: 'Large (10+ items)', value: data.largeBaskets, percentage: ((data.largeBaskets / data.totalTransactions) * 100).toFixed(1) }
  ];

  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percentage }) => `${name}: ${percentage}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <RechartsTooltip />
      </PieChart>
    </ResponsiveContainer>
  );
};

const CustomerInsightsRadar = ({ insights }) => {
  const radarData = [
    { subject: 'Satisfaction', A: insights.customerSatisfactionScore * 20, fullMark: 100 },
    { subject: 'Recommendation', A: insights.recommendationRate, fullMark: 100 },
    { subject: 'Shopping Speed', A: (10 - insights.averageShoppingDuration) * 10, fullMark: 100 },
    { subject: 'Basket Value', A: 75, fullMark: 100 }, // Mock data
    { subject: 'Return Rate', A: 85, fullMark: 100 }, // Mock data
    { subject: 'Loyalty', A: 70, fullMark: 100 } // Mock data
  ];

  return (
    <ResponsiveContainer width="100%" height={300}>
      <RadarChart data={radarData}>
        <PolarGrid />
        <PolarAngleAxis dataKey="subject" />
        <PolarRadiusAxis angle={90} domain={[0, 100]} />
        <Radar name="Customer Metrics" dataKey="A" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
      </RadarChart>
    </ResponsiveContainer>
  );
};

const EnhancedCustomerAnalytics = ({ storeId }) => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('week');

  const fetchCustomerAnalytics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `http://localhost:5000/api/analytics/store/${storeId}/customers?timeframe=${timeframe}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setAnalytics(response.data);
    } catch (error) {
      console.error('Error fetching customer analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchCustomerAnalytics();
    }
  }, [storeId, timeframe]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={3}>
        <Typography>Loading customer analytics...</Typography>
      </Box>
    );
  }

  if (!analytics) {
    return (
      <Alert severity="error">
        Failed to load customer analytics. Please try again.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header Controls */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" gutterBottom>
          Customer Behavior Analytics
        </Typography>
        <Box display="flex" gap={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Timeframe</InputLabel>
            <Select
              value={timeframe}
              label="Timeframe"
              onChange={(e) => setTimeframe(e.target.value)}
            >
              <MenuItem value="week">Last Week</MenuItem>
              <MenuItem value="month">Last Month</MenuItem>
            </Select>
          </FormControl>
          <Tooltip title="Refresh Data">
            <IconButton onClick={fetchCustomerAnalytics}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Cart Abandonment Rate"
            value={analytics.cartAbandonmentRate}
            format="percentage"
            icon={<ShoppingCart />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Avg Shopping Duration"
            value={analytics.insights?.averageShoppingDuration}
            format="time"
            icon={<AccessTime />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Customer Satisfaction"
            value={analytics.insights?.customerSatisfactionScore}
            icon={<Star />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Avg Basket Value"
            value={analytics.basketAnalysis?.averageBasketValue}
            format="currency"
            icon={<AttachMoney />}
            color="primary"
          />
        </Grid>
      </Grid>

      {/* Customer Retention Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="New Customers"
            value={analytics.retentionMetrics?.newCustomers}
            icon={<People />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Returning Customers"
            value={analytics.retentionMetrics?.returningCustomers}
            icon={<People />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Avg Visits per Customer"
            value={analytics.retentionMetrics?.averageVisitsPerCustomer}
            icon={<Analytics />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Customer Lifetime Value"
            value={analytics.retentionMetrics?.customerLifetimeValue}
            format="currency"
            icon={<AttachMoney />}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Charts Row 1 */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Shopping Patterns by Hour
              </Typography>
              <ShoppingPatternsChart data={analytics.shoppingPatterns || []} />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Peak Shopping Hours
              </Typography>
              <Box>
                {analytics.peakHours?.slice(0, 5).map((peak, index) => (
                  <Box key={index} display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">
                      {peak._id}:00
                    </Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {peak.transactions} transactions
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Row 2 */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Basket Size Distribution
              </Typography>
              <BasketSizeChart data={analytics.basketAnalysis} />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Customer Experience Metrics
              </Typography>
              <CustomerInsightsRadar insights={analytics.insights || {}} />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Basket Analysis Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Detailed Basket Analysis
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Metric</TableCell>
                  <TableCell align="right">Value</TableCell>
                  <TableCell align="right">Insight</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell>Average Basket Size</TableCell>
                  <TableCell align="right">{analytics.basketAnalysis?.averageBasketSize?.toFixed(1) || 0} items</TableCell>
                  <TableCell align="right">
                    <Chip 
                      label={analytics.basketAnalysis?.averageBasketSize > 5 ? "Good" : "Needs Improvement"} 
                      color={analytics.basketAnalysis?.averageBasketSize > 5 ? "success" : "warning"}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Average Basket Value</TableCell>
                  <TableCell align="right">R{analytics.basketAnalysis?.averageBasketValue?.toFixed(2) || 0}</TableCell>
                  <TableCell align="right">
                    <Chip 
                      label={analytics.basketAnalysis?.averageBasketValue > 100 ? "Excellent" : "Good"} 
                      color={analytics.basketAnalysis?.averageBasketValue > 100 ? "success" : "primary"}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Average Item Price</TableCell>
                  <TableCell align="right">R{analytics.basketAnalysis?.averageItemPrice?.toFixed(2) || 0}</TableCell>
                  <TableCell align="right">
                    <Chip label="Market Rate" color="info" size="small" />
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Cart Abandonment Rate</TableCell>
                  <TableCell align="right">{analytics.cartAbandonmentRate}%</TableCell>
                  <TableCell align="right">
                    <Chip 
                      label={analytics.cartAbandonmentRate < 20 ? "Excellent" : "Needs Attention"} 
                      color={analytics.cartAbandonmentRate < 20 ? "success" : "error"}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default EnhancedCustomerAnalytics;
