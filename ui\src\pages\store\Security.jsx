import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import SecurityCompliance from '../../components/store/security/SecurityCompliance';

const StoreSecurity = () => {
  const [storeData, setStoreData] = useState(null);

  useEffect(() => {
    fetchStoreData();
  }, []);

  const fetchStoreData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/stores', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStoreData(data[0]); // Assuming first store for now
      }
    } catch (error) {
      console.error('Error fetching store data:', error);
    }
  };

  const storeId = storeData?._id;

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Security & Compliance
      </Typography>
      {storeId && <SecurityCompliance storeId={storeId} />}
    </Box>
  );
};

export default StoreSecurity;
