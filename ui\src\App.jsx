import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material';
import CssBaseline from '@mui/material/CssBaseline';
import Login from './pages/Login';
import AdminLayout from './layouts/AdminLayout';
import StoreLayout from './layouts/StoreLayout';
import AdminStores from './pages/admin/Stores';
import AdminSystems from './pages/admin/Systems';
import StoreProducts from './pages/store/Products';
import StoreFlyer from './pages/store/Flyer';
import StoreSystems from './pages/store/Systems';
import Settings from './pages/store/Settings';
import AdminReports from './pages/admin/Reports';
import StoreReports from './pages/store/Reports';
import StoreDashboard from './pages/store/Dashboard';
import StoreAnalytics from './pages/store/Analytics';
import StoreFinancial from './pages/store/Financial';
import StoreCustomers from './pages/store/Customers';
import StoreMarketing from './pages/store/Marketing';
import StoreInventory from './pages/store/Inventory';
import StoreBusinessIntelligence from './pages/store/BusinessIntelligence';
import StoreSecurity from './pages/store/Security';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <BrowserRouter>
        <Routes>
          <Route path="/login" element={<Login />} />
          
          {/* Admin routes */}
          <Route path="/admin" element={<AdminLayout />}>
            <Route index element={<Navigate to="/admin/stores" replace />} />
            <Route path="stores" element={<AdminStores />} />
            <Route path="systems" element={<AdminSystems />} />
            <Route path="reports" element={<AdminReports />} />
          </Route>
          
          {/* Store routes */}
          <Route path="/store" element={<StoreLayout />}>
            <Route index element={<Navigate to="/store/dashboard" replace />} />
            <Route path="dashboard" element={<StoreDashboard />} />
            <Route path="analytics" element={<StoreAnalytics />} />
            <Route path="products" element={<StoreProducts />} />
            <Route path="financial" element={<StoreFinancial />} />
            <Route path="customers" element={<StoreCustomers />} />
            <Route path="systems" element={<StoreSystems />} />
            <Route path="marketing" element={<StoreMarketing />} />
            <Route path="inventory" element={<StoreInventory />} />
            <Route path="business-intelligence" element={<StoreBusinessIntelligence />} />
            <Route path="security" element={<StoreSecurity />} />
            <Route path="flyer" element={<StoreFlyer />} />
            <Route path="settings" element={<Settings />} />
            <Route path="reports" element={<StoreReports />} />
          </Route>
          
          <Route path="/" element={<Navigate to="/login" replace />} />
        </Routes>
      </BrowserRouter>
    </ThemeProvider>
  );
}

export default App;
