const Store = require('../models/Store');

/**
 * Get security data for a store
 */
exports.getSecurityData = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock security data - in production, this would come from security monitoring systems
    const securityData = {
      securityScore: 87,
      lastSecurityScan: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      activeThreats: 0,
      vulnerabilities: 2,
      dataBackupStatus: 'Completed',
      lastBackup: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      encryptionStatus: 'Active',
      firewallStatus: 'Active',
      accessAttempts: {
        successful: 1247,
        failed: 23,
        blocked: 5
      }
    };

    res.status(200).json({ securityData });

  } catch (error) {
    console.error('Security data error:', error);
    res.status(500).json({
      message: 'Failed to get security data',
      error: error.message
    });
  }
};

/**
 * Get compliance status for a store
 */
exports.getComplianceStatus = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock compliance data
    const complianceStatus = [
      {
        id: 1,
        name: 'POPIA Compliance',
        description: 'Protection of Personal Information Act compliance',
        status: 'Compliant',
        lastCheck: '2024-07-01',
        nextCheck: '2024-10-01',
        score: 95
      },
      {
        id: 2,
        name: 'PCI DSS',
        description: 'Payment Card Industry Data Security Standard',
        status: 'Compliant',
        lastCheck: '2024-06-15',
        nextCheck: '2024-09-15',
        score: 92
      },
      {
        id: 3,
        name: 'Data Retention Policy',
        description: 'Customer and transaction data retention compliance',
        status: 'Needs Attention',
        lastCheck: '2024-07-05',
        nextCheck: '2024-08-05',
        score: 78
      },
      {
        id: 4,
        name: 'Security Audit',
        description: 'Regular security assessment and vulnerability testing',
        status: 'Compliant',
        lastCheck: '2024-07-09',
        nextCheck: '2024-10-09',
        score: 87
      }
    ];

    res.status(200).json({ complianceStatus });

  } catch (error) {
    console.error('Compliance status error:', error);
    res.status(500).json({
      message: 'Failed to get compliance status',
      error: error.message
    });
  }
};

/**
 * Get security incidents for a store
 */
exports.getSecurityIncidents = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock incidents data
    const incidents = [
      {
        id: 1,
        type: 'Failed Login Attempt',
        severity: 'Low',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        description: 'Multiple failed login attempts from IP *************',
        status: 'Resolved',
        source: 'System 001'
      },
      {
        id: 2,
        type: 'Unusual Transaction Pattern',
        severity: 'Medium',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        description: 'Detected unusual transaction pattern on System 003',
        status: 'Investigating',
        source: 'System 003'
      }
    ];

    res.status(200).json({ incidents });

  } catch (error) {
    console.error('Security incidents error:', error);
    res.status(500).json({
      message: 'Failed to get security incidents',
      error: error.message
    });
  }
};

/**
 * Get access logs for a store
 */
exports.getAccessLogs = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { limit = 50 } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock access logs
    const accessLogs = Array.from({ length: parseInt(limit) }, (_, i) => ({
      id: i + 1,
      timestamp: new Date(Date.now() - i * 60 * 60 * 1000).toISOString(),
      user: i % 3 === 0 ? 'admin' : 'store_user',
      action: ['login', 'logout', 'view_products', 'update_settings'][i % 4],
      ipAddress: `192.168.1.${100 + (i % 50)}`,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      status: i % 10 === 0 ? 'failed' : 'success'
    }));

    res.status(200).json({ accessLogs });

  } catch (error) {
    console.error('Access logs error:', error);
    res.status(500).json({
      message: 'Failed to get access logs',
      error: error.message
    });
  }
};

/**
 * Update security settings for a store
 */
exports.updateSecuritySettings = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { settings } = req.body;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // In production, update security settings in database
    const updatedSettings = {
      ...settings,
      updatedAt: new Date(),
      updatedBy: req.user.id
    };

    res.status(200).json({ 
      message: "Security settings updated successfully", 
      settings: updatedSettings 
    });

  } catch (error) {
    console.error('Update security settings error:', error);
    res.status(500).json({
      message: 'Failed to update security settings',
      error: error.message
    });
  }
};

/**
 * Run security scan for a store
 */
exports.runSecurityScan = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock security scan results
    const scanResults = {
      scanId: Date.now(),
      timestamp: new Date().toISOString(),
      status: 'completed',
      score: Math.floor(Math.random() * 20) + 80, // 80-100
      vulnerabilities: Math.floor(Math.random() * 5),
      threats: Math.floor(Math.random() * 2),
      recommendations: [
        'Update system passwords',
        'Enable two-factor authentication',
        'Review access permissions'
      ]
    };

    res.status(200).json({ 
      message: "Security scan completed", 
      results: scanResults 
    });

  } catch (error) {
    console.error('Security scan error:', error);
    res.status(500).json({
      message: 'Failed to run security scan',
      error: error.message
    });
  }
};
