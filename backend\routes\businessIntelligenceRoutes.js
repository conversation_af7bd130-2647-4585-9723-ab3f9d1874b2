const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  getBusinessIntelligenceData,
  getForecasts,
  getInsights,
  getReports,
  generateReport
} = require("../controllers/businessIntelligenceController");

const router = express.Router();

// Get business intelligence data for a store
router.get("/store/:storeId", authenticate, authorize(["admin", "store"]), getBusinessIntelligenceData);

// Get forecasts for a store
router.get("/store/:storeId/forecasts", authenticate, authorize(["admin", "store"]), getForecasts);

// Get insights for a store
router.get("/store/:storeId/insights", authenticate, authorize(["admin", "store"]), getInsights);

// Get reports for a store
router.get("/store/:storeId/reports", authenticate, authorize(["admin", "store"]), getReports);

// Generate a new report
router.post("/store/:storeId/reports", authenticate, authorize(["admin", "store"]), generateReport);

module.exports = router;
