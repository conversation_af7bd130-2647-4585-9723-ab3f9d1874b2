const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  getSecurityData,
  getComplianceStatus,
  getSecurityIncidents,
  getAccessLogs,
  updateSecuritySettings,
  runSecurityScan
} = require("../controllers/securityController");

const router = express.Router();

// Get security data for a store
router.get("/store/:storeId", authenticate, authorize(["admin", "store"]), getSecurityData);

// Get compliance status for a store
router.get("/store/:storeId/compliance", authenticate, authorize(["admin", "store"]), getComplianceStatus);

// Get security incidents for a store
router.get("/store/:storeId/incidents", authenticate, authorize(["admin", "store"]), getSecurityIncidents);

// Get access logs for a store
router.get("/store/:storeId/logs", authenticate, authorize(["admin", "store"]), getAccessLogs);

// Update security settings for a store
router.put("/store/:storeId/settings", authenticate, authorize(["admin", "store"]), updateSecuritySettings);

// Run security scan for a store
router.post("/store/:storeId/scan", authenticate, authorize(["admin", "store"]), runSecurityScan);

module.exports = router;
