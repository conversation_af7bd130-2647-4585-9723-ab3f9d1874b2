const Product = require('../models/Product');
const Store = require('../models/Store');

/**
 * Get inventory data for a store
 */
exports.getInventoryData = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get products with inventory information
    const products = await Product.find({ storeId }).select(
      'name barcode category price stock reorderPoint maxStock supplier lastRestocked'
    );

    // Transform products to inventory format
    const inventory = products.map(product => ({
      id: product._id,
      name: product.name,
      barcode: product.barcode,
      category: product.category || 'General',
      currentStock: product.stock || Math.floor(Math.random() * 100), // Use real stock or mock
      reorderPoint: product.reorderPoint || 20,
      maxStock: product.maxStock || 100,
      unitCost: product.price * 0.7, // Assume 30% markup
      sellingPrice: product.price,
      supplier: product.supplier || 'Default Supplier',
      lastRestocked: product.lastRestocked || new Date().toISOString().split('T')[0],
      status: (product.stock || 0) < (product.reorderPoint || 20) ? 'low_stock' : 'in_stock'
    }));

    res.status(200).json({ inventory });

  } catch (error) {
    console.error('Inventory data error:', error);
    res.status(500).json({
      message: 'Failed to get inventory data',
      error: error.message
    });
  }
};

/**
 * Get suppliers for a store
 */
exports.getSuppliers = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get unique suppliers from products
    const suppliers = await Product.aggregate([
      { $match: { storeId: require('mongoose').Types.ObjectId(storeId) } },
      { $group: { _id: '$supplier', products: { $sum: 1 } } },
      { $match: { _id: { $ne: null } } }
    ]);

    // Transform to supplier format with mock data
    const supplierData = suppliers.map((supplier, index) => ({
      id: index + 1,
      name: supplier._id || 'Default Supplier',
      contact: 'Contact Person',
      email: `contact@${supplier._id?.toLowerCase().replace(/\s+/g, '')}.com`,
      phone: '+27 11 123 4567',
      products: supplier.products,
      lastOrder: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'active'
    }));

    res.status(200).json({ suppliers: supplierData });

  } catch (error) {
    console.error('Suppliers error:', error);
    res.status(500).json({
      message: 'Failed to get suppliers',
      error: error.message
    });
  }
};

/**
 * Get stock alerts for a store
 */
exports.getStockAlerts = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get products with low stock
    const lowStockProducts = await Product.find({
      storeId,
      $expr: { $lt: ['$stock', '$reorderPoint'] }
    }).select('name stock reorderPoint');

    const alerts = lowStockProducts.map(product => ({
      productId: product._id,
      productName: product.name,
      message: product.stock === 0 
        ? 'Out of stock - immediate reorder required'
        : `Stock level is below reorder point (${product.stock} remaining)`,
      level: product.stock === 0 ? 'error' : 'warning',
      stockLevel: product.stock || 0,
      reorderPoint: product.reorderPoint || 20
    }));

    res.status(200).json({ alerts });

  } catch (error) {
    console.error('Stock alerts error:', error);
    res.status(500).json({
      message: 'Failed to get stock alerts',
      error: error.message
    });
  }
};

/**
 * Get inventory analytics for a store
 */
exports.getInventoryAnalytics = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const products = await Product.find({ storeId });
    
    const analytics = {
      totalProducts: products.length,
      totalValue: products.reduce((sum, p) => sum + (p.price * (p.stock || 0)), 0),
      lowStockItems: products.filter(p => (p.stock || 0) < (p.reorderPoint || 20)).length,
      outOfStockItems: products.filter(p => (p.stock || 0) === 0).length,
      averageStockLevel: products.reduce((sum, p) => sum + (p.stock || 0), 0) / products.length || 0,
      topCategories: await Product.aggregate([
        { $match: { storeId: require('mongoose').Types.ObjectId(storeId) } },
        { $group: { _id: '$category', count: { $sum: 1 }, value: { $sum: { $multiply: ['$price', '$stock'] } } } },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ])
    };

    res.status(200).json({ analytics });

  } catch (error) {
    console.error('Inventory analytics error:', error);
    res.status(500).json({
      message: 'Failed to get inventory analytics',
      error: error.message
    });
  }
};

/**
 * Update product stock
 */
exports.updateStock = async (req, res) => {
  try {
    const { storeId, productId } = req.params;
    const { stock, reorderPoint, maxStock } = req.body;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const product = await Product.findOneAndUpdate(
      { _id: productId, storeId },
      { stock, reorderPoint, maxStock, lastRestocked: new Date() },
      { new: true }
    );

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    res.status(200).json({ message: "Stock updated successfully", product });

  } catch (error) {
    console.error('Update stock error:', error);
    res.status(500).json({
      message: 'Failed to update stock',
      error: error.message
    });
  }
};
