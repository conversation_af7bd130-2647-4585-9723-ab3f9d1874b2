const Transaction = require('../models/Transaction');
const Product = require('../models/Product');
const Store = require('../models/Store');

/**
 * Get business intelligence data for a store
 */
exports.getBusinessIntelligenceData = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { dateRange = '30d' } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Calculate date range
    const now = new Date();
    let startDate;
    switch (dateRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get transaction data for KPIs
    const transactions = await Transaction.find({
      storeId,
      status: 'completed',
      createdAt: { $gte: startDate }
    });

    const totalRevenue = transactions.reduce((sum, t) => sum + t.amount, 0);
    const totalTransactions = transactions.length;

    // Mock KPIs with some real data
    const biData = {
      kpis: {
        revenueGrowth: 12.5,
        customerGrowth: 8.3,
        profitMargin: 23.7,
        inventoryTurnover: 4.2,
        customerSatisfaction: 4.6,
        marketShare: 15.8
      },
      revenue: {
        current: totalRevenue,
        previous: totalRevenue * 0.9, // Mock previous period
        growth: 12.5
      },
      transactions: {
        current: totalTransactions,
        previous: Math.floor(totalTransactions * 0.92),
        growth: 8.7
      },
      customers: {
        total: Math.floor(totalTransactions * 0.7), // Assume some repeat customers
        new: Math.floor(totalTransactions * 0.4),
        returning: Math.floor(totalTransactions * 0.3)
      }
    };

    res.status(200).json({ biData });

  } catch (error) {
    console.error('Business intelligence data error:', error);
    res.status(500).json({
      message: 'Failed to get business intelligence data',
      error: error.message
    });
  }
};

/**
 * Get forecasts for a store
 */
exports.getForecasts = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get historical data for forecasting
    const historicalData = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Generate mock forecasts based on historical trends
    const forecasts = [
      {
        id: 1,
        type: 'revenue',
        period: 'next_week',
        prediction: historicalData.length > 0 
          ? historicalData.slice(-7).reduce((sum, d) => sum + d.revenue, 0) * 1.1
          : 25000,
        confidence: 85,
        factors: ['Seasonal trends', 'Historical performance', 'Market conditions']
      },
      {
        id: 2,
        type: 'sales',
        period: 'next_month',
        prediction: historicalData.length > 0 
          ? historicalData.slice(-30).reduce((sum, d) => sum + d.transactions, 0) * 1.05
          : 450,
        confidence: 78,
        factors: ['Customer behavior', 'Product availability', 'Marketing campaigns']
      }
    ];

    res.status(200).json({ forecasts });

  } catch (error) {
    console.error('Forecasts error:', error);
    res.status(500).json({
      message: 'Failed to get forecasts',
      error: error.message
    });
  }
};

/**
 * Get business insights for a store
 */
exports.getInsights = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get top products
    const topProducts = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed'
        }
      },
      { $unwind: '$products' },
      {
        $group: {
          _id: '$products.name',
          sales: { $sum: '$products.quantity' },
          revenue: { $sum: { $multiply: ['$products.price', '$products.quantity'] } }
        }
      },
      { $sort: { revenue: -1 } },
      { $limit: 5 }
    ]);

    // Generate insights
    const insights = [
      {
        id: 1,
        type: 'opportunity',
        title: 'Peak Hour Optimization',
        description: 'Your busiest hour is 3-4 PM. Consider staffing adjustments.',
        impact: 'high',
        category: 'operations'
      },
      {
        id: 2,
        type: 'warning',
        title: 'Inventory Alert',
        description: '5 products are below reorder point.',
        impact: 'medium',
        category: 'inventory'
      },
      {
        id: 3,
        type: 'success',
        title: 'Revenue Growth',
        description: 'Revenue increased by 12% compared to last month.',
        impact: 'high',
        category: 'financial'
      }
    ];

    res.status(200).json({ insights, topProducts });

  } catch (error) {
    console.error('Insights error:', error);
    res.status(500).json({
      message: 'Failed to get insights',
      error: error.message
    });
  }
};

/**
 * Get business intelligence reports for a store
 */
exports.getReports = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock reports data
    const reports = [
      {
        id: 1,
        name: 'Monthly Performance Report',
        type: 'performance',
        generatedAt: new Date().toISOString(),
        status: 'ready',
        size: '2.3 MB'
      },
      {
        id: 2,
        name: 'Customer Behavior Analysis',
        type: 'customer',
        generatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        status: 'ready',
        size: '1.8 MB'
      },
      {
        id: 3,
        name: 'Inventory Optimization Report',
        type: 'inventory',
        generatedAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
        status: 'ready',
        size: '1.2 MB'
      }
    ];

    res.status(200).json({ reports });

  } catch (error) {
    console.error('Reports error:', error);
    res.status(500).json({
      message: 'Failed to get reports',
      error: error.message
    });
  }
};

/**
 * Generate a new business intelligence report
 */
exports.generateReport = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { reportType, dateRange } = req.body;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock report generation
    const newReport = {
      id: Date.now(),
      name: `${reportType} Report - ${new Date().toLocaleDateString()}`,
      type: reportType,
      generatedAt: new Date().toISOString(),
      status: 'generating',
      size: 'Calculating...',
      dateRange
    };

    // Simulate report generation delay
    setTimeout(() => {
      newReport.status = 'ready';
      newReport.size = `${(Math.random() * 3 + 1).toFixed(1)} MB`;
    }, 3000);

    res.status(201).json({ 
      message: "Report generation started", 
      report: newReport 
    });

  } catch (error) {
    console.error('Generate report error:', error);
    res.status(500).json({
      message: 'Failed to generate report',
      error: error.message
    });
  }
};
