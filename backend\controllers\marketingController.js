const Store = require('../models/Store');

/**
 * Get marketing campaigns for a store
 */
exports.getCampaigns = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock campaigns data - in production, this would come from a campaigns collection
    const campaigns = [
      {
        id: 1,
        name: 'Summer Sale 2024',
        type: 'email',
        status: 'active',
        startDate: '2024-06-01',
        endDate: '2024-08-31',
        reach: 1250,
        engagement: 18.5,
        conversions: 89,
        revenue: 15420
      },
      {
        id: 2,
        name: 'Back to School',
        type: 'sms',
        status: 'scheduled',
        startDate: '2024-08-15',
        endDate: '2024-09-15',
        reach: 850,
        engagement: 0,
        conversions: 0,
        revenue: 0
      }
    ];

    res.status(200).json({ campaigns });

  } catch (error) {
    console.error('Campaigns error:', error);
    res.status(500).json({
      message: 'Failed to get campaigns',
      error: error.message
    });
  }
};

/**
 * Get promotions for a store
 */
exports.getPromotions = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock promotions data
    const promotions = [
      {
        id: 1,
        name: '20% Off Electronics',
        type: 'percentage',
        value: 20,
        category: 'Electronics',
        status: 'active',
        startDate: '2024-07-01',
        endDate: '2024-07-31',
        usageCount: 156,
        maxUsage: 500
      },
      {
        id: 2,
        name: 'Buy 2 Get 1 Free',
        type: 'bogo',
        category: 'Groceries',
        status: 'active',
        startDate: '2024-07-10',
        endDate: '2024-07-20',
        usageCount: 89,
        maxUsage: 200
      }
    ];

    res.status(200).json({ promotions });

  } catch (error) {
    console.error('Promotions error:', error);
    res.status(500).json({
      message: 'Failed to get promotions',
      error: error.message
    });
  }
};

/**
 * Get loyalty programs for a store
 */
exports.getLoyaltyPrograms = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock loyalty programs data
    const loyaltyPrograms = [
      {
        id: 1,
        name: 'VIP Rewards',
        type: 'points',
        pointsPerRand: 1,
        rewardThreshold: 100,
        activeMembers: 342,
        totalRewards: 1250,
        status: 'active'
      }
    ];

    res.status(200).json({ loyaltyPrograms });

  } catch (error) {
    console.error('Loyalty programs error:', error);
    res.status(500).json({
      message: 'Failed to get loyalty programs',
      error: error.message
    });
  }
};

/**
 * Get marketing analytics for a store
 */
exports.getMarketingAnalytics = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock analytics data
    const analytics = {
      totalCampaigns: 12,
      activeCampaigns: 3,
      totalReach: 15420,
      avgEngagement: 16.8,
      totalConversions: 234,
      marketingROI: 3.2,
      customerAcquisitionCost: 45.50,
      lifetimeValue: 890.25
    };

    res.status(200).json({ analytics });

  } catch (error) {
    console.error('Marketing analytics error:', error);
    res.status(500).json({
      message: 'Failed to get marketing analytics',
      error: error.message
    });
  }
};

/**
 * Create a new campaign
 */
exports.createCampaign = async (req, res) => {
  try {
    const { storeId } = req.params;
    const campaignData = req.body;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // In production, save to campaigns collection
    const newCampaign = {
      id: Date.now(),
      ...campaignData,
      storeId,
      createdAt: new Date(),
      status: 'draft'
    };

    res.status(201).json({ 
      message: "Campaign created successfully", 
      campaign: newCampaign 
    });

  } catch (error) {
    console.error('Create campaign error:', error);
    res.status(500).json({
      message: 'Failed to create campaign',
      error: error.message
    });
  }
};

/**
 * Create a new promotion
 */
exports.createPromotion = async (req, res) => {
  try {
    const { storeId } = req.params;
    const promotionData = req.body;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // In production, save to promotions collection
    const newPromotion = {
      id: Date.now(),
      ...promotionData,
      storeId,
      createdAt: new Date(),
      status: 'active'
    };

    res.status(201).json({ 
      message: "Promotion created successfully", 
      promotion: newPromotion 
    });

  } catch (error) {
    console.error('Create promotion error:', error);
    res.status(500).json({
      message: 'Failed to create promotion',
      error: error.message
    });
  }
};
