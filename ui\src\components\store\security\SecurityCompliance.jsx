import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  LinearProgress,
  Avatar,
  Divider,
  Tab,
  Tabs,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Security,
  Shield,
  Lock,
  Visibility,
  VisibilityOff,
  Warning,
  CheckCircle,
  Error,
  Info,
  Person,
  Group,
  Key,
  VpnKey,
  Fingerprint,
  Assessment,
  History,
  Gavel,
  VerifiedUser,
  Policy,
  Backup,
  CloudSync,
  MonitorHeart,
  Notifications,
  Report,
  Download,
  Refresh,
  Add,
  Edit,
  Delete,
  Settings,
  AdminPanelSettings,
  DataUsage,
  PrivacyTip
} from '@mui/icons-material';

const SecurityCompliance = ({ storeId }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [securityData, setSecurityData] = useState(null);
  const [auditLogs, setAuditLogs] = useState([]);
  const [userAccess, setUserAccess] = useState([]);
  const [complianceStatus, setComplianceStatus] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSecurityData();
  }, [storeId]);

  const fetchSecurityData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      // Fetch security data
      const securityResponse = await fetch(`http://localhost:5000/api/security/store/${storeId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (securityResponse.ok) {
        const data = await securityResponse.json();
        setSecurityData(data.securityData || {});
      }

      setAuditLogs([
        {
          id: 1,
          timestamp: '2024-07-10 10:30:15',
          user: '<EMAIL>',
          action: 'User Login',
          resource: 'Dashboard',
          ipAddress: '*************',
          status: 'Success',
          severity: 'info'
        },
        {
          id: 2,
          timestamp: '2024-07-10 10:25:42',
          user: '<EMAIL>',
          action: 'Product Update',
          resource: 'Product ID: 12345',
          ipAddress: '*************',
          status: 'Success',
          severity: 'info'
        },
        {
          id: 3,
          timestamp: '2024-07-10 09:15:33',
          user: 'unknown',
          action: 'Failed Login Attempt',
          resource: 'Login Page',
          ipAddress: '************',
          status: 'Failed',
          severity: 'warning'
        },
        {
          id: 4,
          timestamp: '2024-07-10 08:45:12',
          user: 'system',
          action: 'Backup Completed',
          resource: 'Database',
          ipAddress: 'localhost',
          status: 'Success',
          severity: 'info'
        }
      ]);

      setUserAccess([
        {
          id: 1,
          name: 'John Smith',
          email: '<EMAIL>',
          role: 'Store Manager',
          lastLogin: '2024-07-10 10:30',
          status: 'Active',
          permissions: ['Dashboard', 'Products', 'Reports', 'Settings']
        },
        {
          id: 2,
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          role: 'Cashier',
          lastLogin: '2024-07-10 09:15',
          status: 'Active',
          permissions: ['POS System', 'Basic Reports']
        },
        {
          id: 3,
          name: 'Mike Wilson',
          email: '<EMAIL>',
          role: 'Inventory Manager',
          lastLogin: '2024-07-09 16:45',
          status: 'Active',
          permissions: ['Inventory', 'Products', 'Suppliers']
        }
      ]);

      // Fetch compliance status
      const complianceResponse = await fetch(`http://localhost:5000/api/security/store/${storeId}/compliance`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (complianceResponse.ok) {
        const complianceData = await complianceResponse.json();
        setComplianceStatus(complianceData.complianceStatus || []);
      }

      // Fetch security incidents
      const incidentsResponse = await fetch(`http://localhost:5000/api/security/store/${storeId}/incidents`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (incidentsResponse.ok) {
        const incidentsData = await incidentsResponse.json();
        setSecurityIncidents(incidentsData.incidents || []);
      }

      // Fetch access logs
      const logsResponse = await fetch(`http://localhost:5000/api/security/store/${storeId}/logs?limit=20`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        setAccessLogs(logsData.accessLogs || []);
      }

    } catch (error) {
      console.error('Error fetching security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'compliant':
      case 'active':
      case 'success':
        return 'success';
      case 'needs attention':
      case 'warning':
        return 'warning';
      case 'non-compliant':
      case 'failed':
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'info': return <Info color="info" />;
      case 'warning': return <Warning color="warning" />;
      case 'error': return <Error color="error" />;
      case 'success': return <CheckCircle color="success" />;
      default: return <Info />;
    }
  };

  const MetricCard = ({ title, value, change, icon, color = 'primary', format = 'number', suffix = '' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {format === 'percentage' ? `${value || 0}%` : 
               value?.toLocaleString() || 0}{suffix}
            </Typography>
            {change !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                <Typography
                  variant="body2"
                  color={change >= 0 ? 'success.main' : 'error.main'}
                >
                  {change >= 0 ? '+' : ''}{change}%
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.light` }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Security & Compliance</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchSecurityData}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<Assessment />}
          >
            Security Scan
          </Button>
          <Button
            variant="contained"
            startIcon={<Report />}
          >
            Generate Report
          </Button>
        </Box>
      </Box>

      {/* Security Overview */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Security Score"
            value={securityData?.securityScore}
            format="percentage"
            icon={<Shield />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Active Threats"
            value={securityData?.activeThreats}
            icon={<Warning />}
            color={securityData?.activeThreats > 0 ? 'error' : 'success'}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Vulnerabilities"
            value={securityData?.vulnerabilities}
            icon={<Security />}
            color={securityData?.vulnerabilities > 0 ? 'warning' : 'success'}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Failed Logins"
            value={securityData?.accessAttempts?.failed}
            icon={<Lock />}
            color={securityData?.accessAttempts?.failed > 10 ? 'warning' : 'info'}
          />
        </Grid>
      </Grid>

      {/* Security Alerts */}
      {securityData?.vulnerabilities > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Security Alert:</strong> {securityData.vulnerabilities} vulnerabilities detected. 
            Please review and address these issues immediately.
          </Typography>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            label="Security Monitor"
            icon={<MonitorHeart />}
            iconPosition="start"
          />
          <Tab
            label="User Access"
            icon={<AdminPanelSettings />}
            iconPosition="start"
          />
          <Tab
            label="Audit Logs"
            icon={<History />}
            iconPosition="start"
          />
          <Tab
            label="Compliance"
            icon={<Gavel />}
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* Security Monitor Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" mb={3}>Security Monitoring</Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>System Status</Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <Shield color={securityData?.firewallStatus === 'Active' ? 'success' : 'error'} />
                          </ListItemIcon>
                          <ListItemText
                            primary="Firewall"
                            secondary={securityData?.firewallStatus}
                          />
                          <ListItemSecondaryAction>
                            <Chip
                              label={securityData?.firewallStatus}
                              color={getStatusColor(securityData?.firewallStatus)}
                              size="small"
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <Lock color={securityData?.encryptionStatus === 'Active' ? 'success' : 'error'} />
                          </ListItemIcon>
                          <ListItemText
                            primary="Data Encryption"
                            secondary={securityData?.encryptionStatus}
                          />
                          <ListItemSecondaryAction>
                            <Chip
                              label={securityData?.encryptionStatus}
                              color={getStatusColor(securityData?.encryptionStatus)}
                              size="small"
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <Backup color={securityData?.dataBackupStatus === 'Completed' ? 'success' : 'warning'} />
                          </ListItemIcon>
                          <ListItemText
                            primary="Data Backup"
                            secondary={`Last backup: ${securityData?.lastBackup}`}
                          />
                          <ListItemSecondaryAction>
                            <Chip
                              label={securityData?.dataBackupStatus}
                              color={getStatusColor(securityData?.dataBackupStatus)}
                              size="small"
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Access Statistics</Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={4}>
                          <Typography variant="body2" color="textSecondary">Successful</Typography>
                          <Typography variant="h5" color="success.main">
                            {securityData?.accessAttempts?.successful}
                          </Typography>
                        </Grid>
                        <Grid item xs={4}>
                          <Typography variant="body2" color="textSecondary">Failed</Typography>
                          <Typography variant="h5" color="warning.main">
                            {securityData?.accessAttempts?.failed}
                          </Typography>
                        </Grid>
                        <Grid item xs={4}>
                          <Typography variant="body2" color="textSecondary">Blocked</Typography>
                          <Typography variant="h5" color="error.main">
                            {securityData?.accessAttempts?.blocked}
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* User Access Tab */}
          {activeTab === 1 && (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">User Access Management</Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                >
                  Add User
                </Button>
              </Box>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>User</TableCell>
                      <TableCell>Role</TableCell>
                      <TableCell>Last Login</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Permissions</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {userAccess.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {user.name}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {user.email}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{user.role}</TableCell>
                        <TableCell>{user.lastLogin}</TableCell>
                        <TableCell>
                          <Chip
                            label={user.status}
                            color={getStatusColor(user.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Box display="flex" flexWrap="wrap" gap={0.5}>
                            {user.permissions.slice(0, 2).map((permission) => (
                              <Chip
                                key={permission}
                                label={permission}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                            {user.permissions.length > 2 && (
                              <Chip
                                label={`+${user.permissions.length - 2}`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" gap={1}>
                            <IconButton size="small">
                              <Edit />
                            </IconButton>
                            <IconButton size="small">
                              <Key />
                            </IconButton>
                            <IconButton size="small" color="error">
                              <Delete />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Audit Logs Tab */}
          {activeTab === 2 && (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">Audit Logs</Typography>
                <Box display="flex" gap={2}>
                  <TextField
                    size="small"
                    placeholder="Search logs..."
                    sx={{ width: 200 }}
                  />
                  <Button startIcon={<Download />}>Export</Button>
                </Box>
              </Box>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Timestamp</TableCell>
                      <TableCell>User</TableCell>
                      <TableCell>Action</TableCell>
                      <TableCell>Resource</TableCell>
                      <TableCell>IP Address</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {auditLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>{log.timestamp}</TableCell>
                        <TableCell>{log.user}</TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            {getSeverityIcon(log.severity)}
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              {log.action}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{log.resource}</TableCell>
                        <TableCell>{log.ipAddress}</TableCell>
                        <TableCell>
                          <Chip
                            label={log.status}
                            color={getStatusColor(log.status)}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Compliance Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" mb={3}>Compliance Status</Typography>

              <Grid container spacing={3}>
                {complianceStatus.map((compliance) => (
                  <Grid item xs={12} md={6} key={compliance.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Typography variant="h6">{compliance.name}</Typography>
                          <Chip
                            label={compliance.status}
                            color={getStatusColor(compliance.status)}
                            size="small"
                          />
                        </Box>

                        <Typography variant="body2" color="textSecondary" mb={2}>
                          {compliance.description}
                        </Typography>

                        <Box mb={2}>
                          <Typography variant="body2" color="textSecondary" mb={1}>
                            Compliance Score: {compliance.score}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={compliance.score}
                            color={compliance.score >= 90 ? 'success' : compliance.score >= 70 ? 'warning' : 'error'}
                          />
                        </Box>

                        <Grid container spacing={2} mb={2}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Last Check</Typography>
                            <Typography variant="body2">{compliance.lastCheck}</Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Next Check</Typography>
                            <Typography variant="body2">{compliance.nextCheck}</Typography>
                          </Grid>
                        </Grid>

                        <Box display="flex" gap={1}>
                          <Button size="small" startIcon={<Assessment />}>
                            Run Check
                          </Button>
                          <Button size="small" startIcon={<Report />}>
                            View Report
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </Box>
      </Card>
    </Box>
  );
};

export default SecurityCompliance;
