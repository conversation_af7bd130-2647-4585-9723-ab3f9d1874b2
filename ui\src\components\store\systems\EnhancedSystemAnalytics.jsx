import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Computer,
  AttachMoney,
  ShoppingCart,
  Schedule,
  Star,
  Refresh,
  Analytics
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Bar,
  <PERSON>Chart,
  Pie,
  Cell
} from 'recharts';
import axios from 'axios';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const MetricCard = ({ title, value, change, icon, color = 'primary', format = 'number' }) => {
  const formatValue = (val) => {
    if (format === 'currency') return `R${val?.toLocaleString() || 0}`;
    if (format === 'percentage') return `${val || 0}%`;
    return val?.toLocaleString() || 0;
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h6" component="div">
              {formatValue(value)}
            </Typography>
            {change !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {change >= 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={change >= 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

const SystemRankingTable = ({ systems }) => {
  const getRankColor = (rank) => {
    if (rank === 1) return 'gold';
    if (rank === 2) return 'silver';
    if (rank === 3) return '#CD7F32';
    return 'default';
  };

  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Rank</TableCell>
            <TableCell>System</TableCell>
            <TableCell align="right">Revenue</TableCell>
            <TableCell align="right">Transactions</TableCell>
            <TableCell align="right">Avg Transaction</TableCell>
            <TableCell align="right">Performance Score</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {systems.map((system) => (
            <TableRow key={system.systemId}>
              <TableCell>
                <Box display="flex" alignItems="center">
                  {system.rank <= 3 && (
                    <Star sx={{ color: getRankColor(system.rank), mr: 1 }} />
                  )}
                  #{system.rank}
                </Box>
              </TableCell>
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    {system.systemId || 'Unknown System'}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    {system.operatingDays} days active
                  </Typography>
                </Box>
              </TableCell>
              <TableCell align="right">
                R{system.totalRevenue?.toLocaleString() || 0}
              </TableCell>
              <TableCell align="right">
                {system.totalTransactions?.toLocaleString() || 0}
              </TableCell>
              <TableCell align="right">
                R{system.averageTransaction?.toFixed(2) || 0}
              </TableCell>
              <TableCell align="right">
                <Box display="flex" alignItems="center" justifyContent="flex-end">
                  <Box width="60px" mr={1}>
                    <LinearProgress
                      variant="determinate"
                      value={system.performanceScore || 0}
                      color={system.performanceScore >= 80 ? 'success' : 
                             system.performanceScore >= 60 ? 'warning' : 'error'}
                    />
                  </Box>
                  <Typography variant="body2">
                    {system.performanceScore || 0}
                  </Typography>
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

const HourlyRevenueChart = ({ data }) => {
  const chartData = data.reduce((acc, item) => {
    const key = `${item.date} ${item.hour}:00`;
    if (!acc[key]) {
      acc[key] = { time: key, hour: item.hour };
    }
    acc[key][item.systemId || 'Unknown'] = item.revenue;
    return acc;
  }, {});

  const formattedData = Object.values(chartData).sort((a, b) => a.hour - b.hour);

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={formattedData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="time" />
        <YAxis />
        <RechartsTooltip formatter={(value) => [`R${value}`, 'Revenue']} />
        {Object.keys(formattedData[0] || {})
          .filter(key => key !== 'time' && key !== 'hour')
          .map((systemId, index) => (
            <Line
              key={systemId}
              type="monotone"
              dataKey={systemId}
              stroke={COLORS[index % COLORS.length]}
              strokeWidth={2}
            />
          ))}
      </LineChart>
    </ResponsiveContainer>
  );
};

const EnhancedSystemAnalytics = ({ storeId }) => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('week');
  const [selectedSystem, setSelectedSystem] = useState('all');

  const fetchSystemAnalytics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({ timeframe });
      if (selectedSystem !== 'all') {
        params.append('systemId', selectedSystem);
      }

      const response = await axios.get(
        `http://localhost:5000/api/analytics/store/${storeId}/systems?${params}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setAnalytics(response.data);
    } catch (error) {
      console.error('Error fetching system analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchSystemAnalytics();
    }
  }, [storeId, timeframe, selectedSystem]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={3}>
        <Typography>Loading system analytics...</Typography>
      </Box>
    );
  }

  if (!analytics) {
    return (
      <Alert severity="error">
        Failed to load system analytics. Please try again.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header Controls */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" gutterBottom>
          Enhanced System Analytics
        </Typography>
        <Box display="flex" gap={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Timeframe</InputLabel>
            <Select
              value={timeframe}
              label="Timeframe"
              onChange={(e) => setTimeframe(e.target.value)}
            >
              <MenuItem value="day">Last 24 Hours</MenuItem>
              <MenuItem value="week">Last Week</MenuItem>
              <MenuItem value="month">Last Month</MenuItem>
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>System</InputLabel>
            <Select
              value={selectedSystem}
              label="System"
              onChange={(e) => setSelectedSystem(e.target.value)}
            >
              <MenuItem value="all">All Systems</MenuItem>
              {analytics.systemPerformance?.map((system) => (
                <MenuItem key={system.systemId} value={system.systemId}>
                  {system.systemId}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Tooltip title="Refresh Data">
            <IconButton onClick={fetchSystemAnalytics}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Summary Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Total Systems"
            value={analytics.summary?.totalSystems}
            icon={<Computer />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Active Systems"
            value={analytics.summary?.activeSystems}
            icon={<Computer />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Total Revenue"
            value={analytics.summary?.totalRevenue}
            format="currency"
            icon={<AttachMoney />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Total Transactions"
            value={analytics.summary?.totalTransactions}
            icon={<ShoppingCart />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Avg System Revenue"
            value={analytics.summary?.averageSystemRevenue}
            format="currency"
            icon={<Analytics />}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Charts Row */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Hourly Revenue by System
              </Typography>
              <HourlyRevenueChart data={analytics.hourlyRevenue || []} />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Peak Performance Times
              </Typography>
              <Box>
                {analytics.peakTimes?.slice(0, 5).map((peak, index) => (
                  <Box key={index} display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">
                      {peak._id.systemId} - {peak._id.hour}:00
                    </Typography>
                    <Typography variant="body2" fontWeight="bold">
                      R{peak.revenue.toLocaleString()}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Performance Rankings */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            System Performance Rankings
          </Typography>
          <SystemRankingTable systems={analytics.systemPerformance || []} />
        </CardContent>
      </Card>
    </Box>
  );
};

export default EnhancedSystemAnalytics;
