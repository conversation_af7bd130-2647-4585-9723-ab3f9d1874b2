import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Chip,
  Divider,
  Paper
} from '@mui/material';
import {
  ExpandMore,
  QrCode,
  Language,
  Campaign,
  Star,
  Preview,
  Save,
  Refresh
} from '@mui/icons-material';

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`receipt-tabpanel-${index}`}
    aria-labelledby={`receipt-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const EnhancedReceiptTemplateManager = ({ storeId }) => {
  const [template, setTemplate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [saveStatus, setSaveStatus] = useState('');

  useEffect(() => {
    if (storeId) {
      fetchTemplate();
    }
  }, [storeId]);

  const fetchTemplate = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5000/api/receipt-templates/${storeId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setTemplate(data.template);
      } else {
        setTemplate(getDefaultEnhancedTemplate());
      }
    } catch (error) {
      console.error('Error fetching template:', error);
      setTemplate(getDefaultEnhancedTemplate());
    } finally {
      setLoading(false);
    }
  };

  const getDefaultEnhancedTemplate = () => ({
    templateName: 'Enhanced Template',
    isActive: true,
    header: {
      storeName: { show: true, text: '', fontSize: 18, alignment: 'center', bold: true },
      storeAddress: { show: true, text: '', fontSize: 12, alignment: 'center' },
      storeContact: { show: true, phone: '', email: '', website: '', fontSize: 10, alignment: 'center' },
      logo: { show: false, path: '', width: 100, height: 50, alignment: 'center' },
      customMessage: { show: false, text: '', fontSize: 12, alignment: 'center' }
    },
    body: {
      showDateTime: true,
      dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
      showTransactionId: true,
      showCashier: false,
      cashierName: 'Self-Checkout',
      itemsTable: {
        showHeaders: true,
        showQuantity: true,
        showUnitPrice: true,
        showTotalPrice: true,
        showBarcode: false,
        fontSize: 10
      },
      pricing: {
        showSubtotal: true,
        showTax: false,
        taxRate: 15,
        showDiscount: false,
        showTotal: true,
        currency: 'R',
        fontSize: 12
      }
    },
    footer: {
      thankYouMessage: { show: true, text: 'Thank you for shopping with us!', fontSize: 12, alignment: 'center' },
      returnPolicy: { show: false, text: '', fontSize: 8, alignment: 'center' },
      socialMedia: { show: false, facebook: '', instagram: '', twitter: '', fontSize: 8, alignment: 'center' },
      customFooter: { show: false, text: '', fontSize: 10, alignment: 'center' },
      qrCode: {
        show: false,
        type: 'feedback',
        url: '',
        size: 100,
        alignment: 'center',
        label: 'Scan for feedback'
      },
      promotionalMessage: {
        show: false,
        text: '',
        fontSize: 10,
        alignment: 'center',
        backgroundColor: '#f0f0f0',
        textColor: '#000000'
      }
    },
    advanced: {
      multiLanguage: {
        enabled: false,
        primaryLanguage: 'en',
        secondaryLanguage: '',
        translations: new Map()
      },
      loyaltyProgram: {
        enabled: false,
        programName: '',
        pointsEarned: false,
        membershipInfo: false,
        qrCodeIntegration: false
      },
      dynamicContent: {
        enabled: false,
        weatherInfo: false,
        dailySpecials: false,
        personalizedOffers: false
      }
    },
    layout: {
      paperWidth: 80,
      margin: 5,
      lineSpacing: 1.2,
      sectionSpacing: 10
    }
  });

  const updateTemplate = (path, value) => {
    setTemplate(prev => {
      const newTemplate = { ...prev };
      const keys = path.split('.');
      let current = newTemplate;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newTemplate;
    });
  };

  const saveTemplate = async () => {
    try {
      setSaveStatus('saving');
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5000/api/receipt-templates/${storeId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(template)
      });

      if (response.ok) {
        setSaveStatus('success');
        setTimeout(() => setSaveStatus(''), 3000);
      } else {
        setSaveStatus('error');
      }
    } catch (error) {
      console.error('Error saving template:', error);
      setSaveStatus('error');
    }
  };

  const previewTemplate = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5000/api/receipt-templates/${storeId}/preview`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(template)
      });

      if (response.ok) {
        const data = await response.json();
        window.open(`${data.receiptPath}`, '_blank');
      } else {
        alert('Failed to generate preview');
      }
    } catch (error) {
      console.error('Error generating preview:', error);
      alert('Error generating preview');
    }
  };

  if (loading) {
    return <Typography>Loading template...</Typography>;
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Enhanced Receipt Template</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Preview />}
            onClick={previewTemplate}
          >
            Preview
          </Button>
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={saveTemplate}
            disabled={saveStatus === 'saving'}
          >
            {saveStatus === 'saving' ? 'Saving...' : 'Save Template'}
          </Button>
        </Box>
      </Box>

      {/* Save Status */}
      {saveStatus === 'success' && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Template saved successfully!
        </Alert>
      )}
      {saveStatus === 'error' && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Failed to save template. Please try again.
        </Alert>
      )}

      {/* Main Content */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <Tabs
              value={activeTab}
              onChange={(e, newValue) => setActiveTab(newValue)}
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab label="Basic Settings" />
              <Tab label="QR Code & Promotions" icon={<QrCode />} />
              <Tab label="Multi-Language" icon={<Language />} />
              <Tab label="Loyalty Program" icon={<Star />} />
              <Tab label="Dynamic Content" icon={<Campaign />} />
            </Tabs>

            <TabPanel value={activeTab} index={0}>
              {/* Basic template settings - header, body, footer */}
              <Typography variant="h6" gutterBottom>Header Settings</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={template?.header?.storeName?.show || false}
                        onChange={(e) => updateTemplate('header.storeName.show', e.target.checked)}
                      />
                    }
                    label="Show Store Name"
                  />
                </Grid>
                {template?.header?.storeName?.show && (
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Store Name"
                      value={template?.header?.storeName?.text || ''}
                      onChange={(e) => updateTemplate('header.storeName.text', e.target.value)}
                    />
                  </Grid>
                )}
              </Grid>
            </TabPanel>

            <TabPanel value={activeTab} index={1}>
              {/* QR Code and Promotional Settings */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography variant="h6">QR Code Settings</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={template?.footer?.qrCode?.show || false}
                            onChange={(e) => updateTemplate('footer.qrCode.show', e.target.checked)}
                          />
                        }
                        label="Show QR Code"
                      />
                    </Grid>
                    {template?.footer?.qrCode?.show && (
                      <>
                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth>
                            <InputLabel>QR Code Type</InputLabel>
                            <Select
                              value={template?.footer?.qrCode?.type || 'feedback'}
                              onChange={(e) => updateTemplate('footer.qrCode.type', e.target.value)}
                            >
                              <MenuItem value="feedback">Customer Feedback</MenuItem>
                              <MenuItem value="website">Store Website</MenuItem>
                              <MenuItem value="loyalty">Loyalty Program</MenuItem>
                              <MenuItem value="custom">Custom URL</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Custom URL"
                            value={template?.footer?.qrCode?.url || ''}
                            onChange={(e) => updateTemplate('footer.qrCode.url', e.target.value)}
                            placeholder="https://example.com"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="QR Code Label"
                            value={template?.footer?.qrCode?.label || ''}
                            onChange={(e) => updateTemplate('footer.qrCode.label', e.target.value)}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            type="number"
                            label="QR Code Size"
                            value={template?.footer?.qrCode?.size || 100}
                            onChange={(e) => updateTemplate('footer.qrCode.size', parseInt(e.target.value))}
                          />
                        </Grid>
                      </>
                    )}
                  </Grid>
                </AccordionDetails>
              </Accordion>

              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography variant="h6">Promotional Message</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={template?.footer?.promotionalMessage?.show || false}
                            onChange={(e) => updateTemplate('footer.promotionalMessage.show', e.target.checked)}
                          />
                        }
                        label="Show Promotional Message"
                      />
                    </Grid>
                    {template?.footer?.promotionalMessage?.show && (
                      <>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            multiline
                            rows={3}
                            label="Promotional Message"
                            value={template?.footer?.promotionalMessage?.text || ''}
                            onChange={(e) => updateTemplate('footer.promotionalMessage.text', e.target.value)}
                            placeholder="Special offers, discounts, or announcements..."
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Text Color"
                            type="color"
                            value={template?.footer?.promotionalMessage?.textColor || '#000000'}
                            onChange={(e) => updateTemplate('footer.promotionalMessage.textColor', e.target.value)}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Background Color"
                            type="color"
                            value={template?.footer?.promotionalMessage?.backgroundColor || '#f0f0f0'}
                            onChange={(e) => updateTemplate('footer.promotionalMessage.backgroundColor', e.target.value)}
                          />
                        </Grid>
                      </>
                    )}
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </TabPanel>

            <TabPanel value={activeTab} index={2}>
              {/* Multi-Language Settings */}
              <Typography variant="h6" gutterBottom>Multi-Language Support</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={template?.advanced?.multiLanguage?.enabled || false}
                        onChange={(e) => updateTemplate('advanced.multiLanguage.enabled', e.target.checked)}
                      />
                    }
                    label="Enable Multi-Language Support"
                  />
                </Grid>
                {template?.advanced?.multiLanguage?.enabled && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Primary Language</InputLabel>
                        <Select
                          value={template?.advanced?.multiLanguage?.primaryLanguage || 'en'}
                          onChange={(e) => updateTemplate('advanced.multiLanguage.primaryLanguage', e.target.value)}
                        >
                          <MenuItem value="en">English</MenuItem>
                          <MenuItem value="af">Afrikaans</MenuItem>
                          <MenuItem value="zu">Zulu</MenuItem>
                          <MenuItem value="xh">Xhosa</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Secondary Language</InputLabel>
                        <Select
                          value={template?.advanced?.multiLanguage?.secondaryLanguage || ''}
                          onChange={(e) => updateTemplate('advanced.multiLanguage.secondaryLanguage', e.target.value)}
                        >
                          <MenuItem value="">None</MenuItem>
                          <MenuItem value="en">English</MenuItem>
                          <MenuItem value="af">Afrikaans</MenuItem>
                          <MenuItem value="zu">Zulu</MenuItem>
                          <MenuItem value="xh">Xhosa</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </>
                )}
              </Grid>
            </TabPanel>

            <TabPanel value={activeTab} index={3}>
              {/* Loyalty Program Settings */}
              <Typography variant="h6" gutterBottom>Loyalty Program Integration</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={template?.advanced?.loyaltyProgram?.enabled || false}
                        onChange={(e) => updateTemplate('advanced.loyaltyProgram.enabled', e.target.checked)}
                      />
                    }
                    label="Enable Loyalty Program"
                  />
                </Grid>
                {template?.advanced?.loyaltyProgram?.enabled && (
                  <>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Program Name"
                        value={template?.advanced?.loyaltyProgram?.programName || ''}
                        onChange={(e) => updateTemplate('advanced.loyaltyProgram.programName', e.target.value)}
                        placeholder="e.g., VIP Rewards, Store Points"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={template?.advanced?.loyaltyProgram?.pointsEarned || false}
                            onChange={(e) => updateTemplate('advanced.loyaltyProgram.pointsEarned', e.target.checked)}
                          />
                        }
                        label="Show Points Earned"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={template?.advanced?.loyaltyProgram?.qrCodeIntegration || false}
                            onChange={(e) => updateTemplate('advanced.loyaltyProgram.qrCodeIntegration', e.target.checked)}
                          />
                        }
                        label="QR Code Integration"
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            </TabPanel>

            <TabPanel value={activeTab} index={4}>
              {/* Dynamic Content Settings */}
              <Typography variant="h6" gutterBottom>Dynamic Content</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={template?.advanced?.dynamicContent?.enabled || false}
                        onChange={(e) => updateTemplate('advanced.dynamicContent.enabled', e.target.checked)}
                      />
                    }
                    label="Enable Dynamic Content"
                  />
                </Grid>
                {template?.advanced?.dynamicContent?.enabled && (
                  <>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={template?.advanced?.dynamicContent?.dailySpecials || false}
                            onChange={(e) => updateTemplate('advanced.dynamicContent.dailySpecials', e.target.checked)}
                          />
                        }
                        label="Show Daily Specials"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={template?.advanced?.dynamicContent?.personalizedOffers || false}
                            onChange={(e) => updateTemplate('advanced.dynamicContent.personalizedOffers', e.target.checked)}
                          />
                        }
                        label="Personalized Offers"
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            </TabPanel>
          </Card>
        </Grid>

        {/* Preview Panel */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Live Preview
              </Typography>
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  backgroundColor: '#f9f9f9',
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  minHeight: '400px'
                }}
              >
                <Box textAlign="center" mb={2}>
                  {template?.header?.storeName?.show && (
                    <Typography variant="h6" fontWeight="bold">
                      {template.header.storeName.text || 'Your Store Name'}
                    </Typography>
                  )}
                  {template?.header?.storeAddress?.show && template.header.storeAddress.text && (
                    <Typography variant="body2">
                      {template.header.storeAddress.text}
                    </Typography>
                  )}
                </Box>
                
                <Divider sx={{ my: 1 }} />
                
                <Typography variant="body2">
                  Date: {new Date().toLocaleString()}
                </Typography>
                <Typography variant="body2">
                  Transaction ID: PREVIEW-123456
                </Typography>
                
                <Divider sx={{ my: 1 }} />
                
                <Typography variant="body2">
                  Sample Product 1    x2    R25.99    R51.98<br />
                  Sample Product 2    x1    R15.50    R15.50
                </Typography>
                
                <Divider sx={{ my: 1 }} />
                
                <Box textAlign="right">
                  <Typography variant="body2" fontWeight="bold">
                    TOTAL: R67.48
                  </Typography>
                </Box>
                
                <Divider sx={{ my: 1 }} />
                
                {template?.footer?.thankYouMessage?.show && (
                  <Box textAlign="center" mt={2}>
                    <Typography variant="body2">
                      {template.footer.thankYouMessage.text}
                    </Typography>
                  </Box>
                )}
                
                {template?.footer?.promotionalMessage?.show && template.footer.promotionalMessage.text && (
                  <Box 
                    textAlign="center" 
                    mt={2} 
                    p={1}
                    sx={{ 
                      backgroundColor: template.footer.promotionalMessage.backgroundColor,
                      color: template.footer.promotionalMessage.textColor,
                      borderRadius: 1
                    }}
                  >
                    <Typography variant="body2">
                      {template.footer.promotionalMessage.text}
                    </Typography>
                  </Box>
                )}
                
                {template?.footer?.qrCode?.show && (
                  <Box textAlign="center" mt={2}>
                    <Box 
                      sx={{ 
                        width: 60, 
                        height: 60, 
                        backgroundColor: '#000', 
                        margin: '0 auto',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '10px'
                      }}
                    >
                      QR
                    </Box>
                    <Typography variant="caption">
                      {template.footer.qrCode.label}
                    </Typography>
                  </Box>
                )}
                
                {template?.advanced?.loyaltyProgram?.enabled && template.advanced.loyaltyProgram.pointsEarned && (
                  <Box textAlign="center" mt={2}>
                    <Chip 
                      label={`${template.advanced.loyaltyProgram.programName || 'Loyalty'}: 67 points earned`}
                      size="small"
                      color="primary"
                    />
                  </Box>
                )}
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EnhancedReceiptTemplateManager;
