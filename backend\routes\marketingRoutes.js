const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  getCampaigns,
  getPromotions,
  getLoyaltyPrograms,
  getMarketingAnalytics,
  createCampaign,
  createPromotion
} = require("../controllers/marketingController");

const router = express.Router();

// Get campaigns for a store
router.get("/store/:storeId/campaigns", authenticate, authorize(["admin", "store"]), getCampaigns);

// Get promotions for a store
router.get("/store/:storeId/promotions", authenticate, authorize(["admin", "store"]), getPromotions);

// Get loyalty programs for a store
router.get("/store/:storeId/loyalty", authenticate, authorize(["admin", "store"]), getLoyaltyPrograms);

// Get marketing analytics for a store
router.get("/store/:storeId/analytics", authenticate, authorize(["admin", "store"]), getMarketingAnalytics);

// Create a new campaign
router.post("/store/:storeId/campaigns", authenticate, authorize(["admin", "store"]), createCampaign);

// Create a new promotion
router.post("/store/:storeId/promotions", authenticate, authorize(["admin", "store"]), createPromotion);

module.exports = router;
