const express = require("express");
const dotenv = require("dotenv");
const cors = require("cors");
const connectDB = require("./config/db");
const authRoutes = require("./routes/authRoutes");
const storeRoutes = require("./routes/storeRoutes");
const productRoutes = require("./routes/productRoutes");
const paymentRoutes = require("./routes/paymentRoutes");
const reportRoutes = require("./routes/reportRoutes");
const analyticsRoutes = require("./routes/analyticsRoutes");
const financialRoutes = require("./routes/financialRoutes");
const errorHandler = require("./middleware/errorHandler"); // Import error handler
const path = require("path");
const swaggerUi = require('swagger-ui-express');
const systemsRoutes = require("./routes/systems");
const receiptTemplateRoutes = require("./routes/receiptTemplateRoutes");
const inventoryRoutes = require("./routes/inventoryRoutes");
const marketingRoutes = require("./routes/marketingRoutes");
const securityRoutes = require("./routes/securityRoutes");
const businessIntelligenceRoutes = require("./routes/businessIntelligenceRoutes");
const multer = require('multer');

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Serve static files from uploads directory
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// Configure CORS
app.use(cors());

app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ storage: storage });

// Make upload available to routes
app.locals.upload = upload;

app.use("/api/auth", authRoutes);
app.use("/api/stores", storeRoutes);
app.use("/api/products", productRoutes);
app.use("/api/payments", paymentRoutes);
app.use("/api/reports", reportRoutes);
app.use("/api/analytics", analyticsRoutes);
app.use("/api/financial", financialRoutes);
app.use("/api/systems", systemsRoutes);
app.use("/api/receipt-templates", receiptTemplateRoutes);
app.use("/api/inventory", inventoryRoutes);
app.use("/api/marketing", marketingRoutes);
app.use("/api/security", securityRoutes);
app.use("/api/business-intelligence", businessIntelligenceRoutes);

// Connect to database
connectDB();

// Use error handler
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});