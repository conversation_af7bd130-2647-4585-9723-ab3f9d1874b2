const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  getInventoryData,
  getSuppliers,
  getStockAlerts,
  getInventoryAnalytics,
  updateStock
} = require("../controllers/inventoryController");

const router = express.Router();

// Get inventory data for a store
router.get("/store/:storeId", authenticate, authorize(["admin", "store"]), getInventoryData);

// Get suppliers for a store
router.get("/store/:storeId/suppliers", authenticate, authorize(["admin", "store"]), getSuppliers);

// Get stock alerts for a store
router.get("/store/:storeId/alerts", authenticate, authorize(["admin", "store"]), getStockAlerts);

// Get inventory analytics for a store
router.get("/store/:storeId/analytics", authenticate, authorize(["admin", "store"]), getInventoryAnalytics);

// Update product stock
router.put("/store/:storeId/product/:productId/stock", authenticate, authorize(["admin", "store"]), updateStock);

module.exports = router;
